const { GoogleGenerativeAI } = require('@google/generative-ai');
const { GoogleAIFileManager } = require("@google/generative-ai/server");
const fs = require('fs');

async function transcribeAudio(audioFilePath, audioMimeType) {
  const apiKey = 'AIzaSyCG_PoCt11a0baF1Ef3vUJOZRbYQ3aIkcM';
  // 1. 检查文件是否存在
  if (!fs.existsSync(audioFilePath)) {
    throw new Error(`音频文件未找到: ${audioFilePath}`);
  }
  // 2. 获取文件大小以决定使用哪种策略
  const stats = fs.statSync(audioFilePath);
  const fileSizeInBytes = stats.size;
  const oneMB = 1 * 1024 * 1024; // 1MB in bytes
  // 3. 初始化AI客户端和模型
  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });
  const prompt = "请将下面的音频内容转录成文字。请直接输出转录后的文本，不要添加任何额外的说明或标题。";
  let parts;
  // 4. 根据文件大小选择不同的处理逻辑
  if (fileSizeInBytes < oneMB) {
    // --- 策略一：文件小于1MB，使用Base64直接上传 ---
    console.log(`文件大小为 ${(fileSizeInBytes / 1024).toFixed(2)} KB (< 1MB)，使用Base64直接上传方法...`);
    const base64Data = fs.readFileSync(audioFilePath).toString('base64');
    parts = [
      { text: prompt },
      {
        inlineData: {
          mimeType: audioMimeType,
          data: base64Data,
        },
      },
    ];
  } else {
    // --- 策略二：文件大于等于1MB，使用File API上传 ---
    console.log(`文件大小为 ${(fileSizeInBytes / (1024 * 1024)).toFixed(2)} MB (>= 1MB)，使用File API上传方法...`);
    const fileManager = new GoogleAIFileManager(apiKey);
    let uploadedFileResponse;
    try {
      console.log("步骤 1/3: 开始上传文件...");
      uploadedFileResponse = await fileManager.uploadFile(
        audioFilePath,
        {
          mimeType: audioMimeType,
          displayName: `speech-to-text-audio-${Date.now()}`,
        }
      );
      const uploadedFile = uploadedFileResponse.file;
      console.log(`步骤 2/3: 文件上传成功！文件资源名: ${uploadedFile.name}`);
      parts = [
        { text: prompt },
        {
          fileData: {
            mimeType: uploadedFile.mimeType,
            fileUri: uploadedFile.uri,
          },
        },
      ];
    } finally {
      // 确保即使转录失败，上传的临时文件也能被删除
      if (uploadedFileResponse) {
        console.log("收尾工作: 正在删除云端临时文件...");
        await fileManager.deleteFile(uploadedFileResponse.file.name);
        console.log("临时文件已删除。");
      }
    }
  }
  // 5. 发送最终请求并获取结果（集成计时功能）
  console.log("步骤 3/3: 发送转录请求到 Gemini API...");
  const startTime = performance.now();
  const result = await model.generateContent({ contents: [{ role: 'user', parts }] });
  const endTime = performance.now();
  const durationInMs = endTime - startTime;
  console.log(`✅ Gemini API 响应成功！耗时: ${(durationInMs / 1000).toFixed(3)} 秒.`);
  const transcript = result.response.text();
  return transcript;
}

async function main() {
  try {
    const myFilePath = './speech/234.wav';
    const myMimeType = 'audio/wav';
    console.log("开始执行语音转录...");
    // 调用核心函数并等待结果
    const transcript = await transcribeAudio(myFilePath, myMimeType);
    // 打印最终的转录文本
    console.log("\n--- 转录结果 ---");
    console.log(transcript);
    console.log("------------------\n");
  } catch (error) {
    // 如果transcribeAudio函数中发生任何错误，这里会捕获到
    console.error("主程序执行失败，已终止。");
  }
}

// 运行主程序
main();